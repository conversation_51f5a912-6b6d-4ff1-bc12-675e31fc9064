//+------------------------------------------------------------------+
//|                        TestTradingPipelineContainerManager_Fixed.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單交易流水線                                           |
//+------------------------------------------------------------------+
class TestManagerTradingPipeline : public TradingPipeline
{
private:
    string m_testMessage;

public:
    TestManagerTradingPipeline(string name, string testMessage = "測試執行")
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage)
    {
    }

protected:
    virtual void Main() override
    {
        Print(StringFormat("[%s] 執行: %s", GetName(), m_testMessage));
    }
};

//+------------------------------------------------------------------+
//| TradingPipelineContainerManager 測試類                          |
//+------------------------------------------------------------------+
class TestTradingPipelineContainerManagerFixed : public TestCase
{
private:
    TestRunner* m_runner;
    bool m_ownsRunner;

public:
    // 構造函數 - 可以接受外部 TestRunner 或創建內部 TestRunner
    TestTradingPipelineContainerManagerFixed(TestRunner* externalRunner = NULL) : TestCase("TestTradingPipelineContainerManagerFixed")
    {
        if(externalRunner != NULL)
        {
            m_runner = externalRunner;
            m_ownsRunner = false;
        }
        else
        {
            m_runner = new TestRunner();
            m_ownsRunner = true;
        }
    }

    ~TestTradingPipelineContainerManagerFixed()
    {
        if(m_ownsRunner && m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    virtual void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestSetContainer();
        TestGetContainer();
        TestRemoveContainer();
        TestExecuteByEvent();
        TestRestoreByEvent();
        TestClear();
        TestEdgeCases();

        // 只有當擁有內部 TestRunner 時才顯示摘要
        if(m_ownsRunner)
        {
            m_runner.ShowSummary();
        }
    }

protected:
    // 記錄測試結果的輔助方法
    void RecordResult(TestResult* result)
    {
        m_runner.RecordResult(result);
    }

    // 測試構造函數
    void TestConstructor()
    {
        Print("=== 測試構造函數 ===");

        // 測試默認參數構造
        TradingPipelineContainerManager* manager1 = new TradingPipelineContainerManager();
        RecordResult(Assert::AssertNotNull("構造_默認參數", manager1));
        RecordResult(Assert::AssertEquals("構造_默認名稱", "TradingPipelineContainerManager", manager1.GetName()));
        RecordResult(Assert::AssertEquals("構造_默認類型", "ContainerManager", manager1.GetType()));
        RecordResult(Assert::AssertEquals("構造_默認最大容器數", 10, manager1.GetMaxContainers()));
        RecordResult(Assert::AssertTrue("構造_默認啟用", manager1.IsEnabled()));

        // 測試自定義參數構造
        TradingPipelineContainerManager* manager2 = new TradingPipelineContainerManager(
            "自定義管理器", "CustomManager", false, 5);
        RecordResult(Assert::AssertEquals("構造_自定義名稱", "自定義管理器", manager2.GetName()));
        RecordResult(Assert::AssertEquals("構造_自定義類型", "CustomManager", manager2.GetType()));
        RecordResult(Assert::AssertEquals("構造_自定義最大容器數", 5, manager2.GetMaxContainers()));

        delete manager1;
        delete manager2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("=== 測試基本屬性 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("屬性測試");

        // 測試初始狀態
        RecordResult(Assert::AssertEquals("屬性_初始容器數", 0, manager.GetContainerCount()));
        RecordResult(Assert::AssertFalse("屬性_初始未執行", manager.IsExecuted()));
        RecordResult(Assert::AssertTrue("屬性_初始有空位", manager.HasEmptySlot()));
        RecordResult(Assert::AssertTrue("屬性_初始為空", manager.IsEmpty()));
        RecordResult(Assert::AssertFalse("屬性_初始未滿", manager.IsFull()));

        // 測試啟用/禁用
        manager.SetEnabled(false);
        RecordResult(Assert::AssertFalse("屬性_禁用後狀態", manager.IsEnabled()));
        manager.SetEnabled(true);
        RecordResult(Assert::AssertTrue("屬性_重新啟用後狀態", manager.IsEnabled()));

        delete manager;
    }

    // 測試設置容器
    void TestSetContainer()
    {
        Print("=== 測試設置容器 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("設置測試");

        // 創建測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("容器1", "第一個容器", "Container");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("容器2", "第二個容器", "Container");

        // 測試設置容器
        bool result1 = manager.SetContainer(TRADING_INIT, container1);
        RecordResult(Assert::AssertTrue("設置_第一個容器", result1));
        RecordResult(Assert::AssertEquals("設置_容器數量1", 1, manager.GetContainerCount()));

        bool result2 = manager.SetContainer(TRADING_TICK, container2);
        RecordResult(Assert::AssertTrue("設置_第二個容器", result2));
        RecordResult(Assert::AssertEquals("設置_容器數量2", 2, manager.GetContainerCount()));

        // 測試設置 NULL 容器
        bool result3 = manager.SetContainer(TRADING_DEINIT, NULL);
        RecordResult(Assert::AssertFalse("設置_NULL容器失敗", result3));

        // 測試覆蓋現有容器
        TradingPipelineContainer* newContainer = new TradingPipelineContainer("新容器", "覆蓋容器", "Container");
        bool result4 = manager.SetContainer(TRADING_INIT, newContainer);
        RecordResult(Assert::AssertTrue("設置_覆蓋現有容器", result4));
        RecordResult(Assert::AssertEquals("設置_覆蓋後容器數量", 2, manager.GetContainerCount()));

        delete manager;
    }

    // 測試獲取容器
    void TestGetContainer()
    {
        Print("=== 測試獲取容器 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("獲取測試");

        // 創建並設置容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("獲取容器1", "測試容器1", "Container");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("獲取容器2", "測試容器2", "Container");

        manager.SetContainer(TRADING_INIT, container1);
        manager.SetContainer(TRADING_TICK, container2);

        // 測試獲取存在的容器
        TradingPipelineContainer* found1 = manager.GetContainer(TRADING_INIT);
        RecordResult(Assert::AssertNotNull("獲取_存在容器1", found1));
        if(found1 != NULL)
        {
            RecordResult(Assert::AssertEquals("獲取_容器1名稱", "獲取容器1", found1.GetName()));
        }

        TradingPipelineContainer* found2 = manager.GetContainer(TRADING_TICK);
        RecordResult(Assert::AssertNotNull("獲取_存在容器2", found2));
        if(found2 != NULL)
        {
            RecordResult(Assert::AssertEquals("獲取_容器2名稱", "獲取容器2", found2.GetName()));
        }

        // 測試獲取不存在的容器
        TradingPipelineContainer* notFound = manager.GetContainer(TRADING_DEINIT);
        RecordResult(Assert::AssertNull("獲取_不存在容器", notFound));

        delete manager;
    }

    // 測試移除容器
    void TestRemoveContainer()
    {
        Print("=== 測試移除容器 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("移除測試");

        // 創建並設置容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("移除容器1", "測試容器1", "Container");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("移除容器2", "測試容器2", "Container");

        manager.SetContainer(TRADING_INIT, container1);
        manager.SetContainer(TRADING_TICK, container2);

        // 測試移除容器
        bool result1 = manager.RemoveContainer(container1);
        RecordResult(Assert::AssertTrue("移除_第一個容器", result1));
        RecordResult(Assert::AssertEquals("移除_容器數量減少", 1, manager.GetContainerCount()));

        // 測試按名稱移除容器
        bool result2 = manager.RemoveContainer("移除容器2");
        RecordResult(Assert::AssertTrue("移除_按名稱移除", result2));
        RecordResult(Assert::AssertEquals("移除_全部移除後數量", 0, manager.GetContainerCount()));

        // 測試移除不存在的容器
        TradingPipelineContainer* nonExistent = new TradingPipelineContainer("不存在", "不存在的容器", "Container");
        bool result3 = manager.RemoveContainer(nonExistent);
        RecordResult(Assert::AssertFalse("移除_不存在容器", result3));

        delete nonExistent;
        delete container1;
        delete container2;
        delete manager;
    }

    // 測試按事件執行
    void TestExecuteByEvent()
    {
        Print("=== 測試按事件執行 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("執行測試");

        // 創建容器和流水線
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("初始化容器", "初始化", "Container");
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("Tick容器", "Tick處理", "Container");

        TestManagerTradingPipeline* initPipeline = new TestManagerTradingPipeline("初始化流水線", "執行初始化");
        TestManagerTradingPipeline* tickPipeline = new TestManagerTradingPipeline("Tick流水線", "執行Tick");

        initContainer.AddPipeline(initPipeline);
        tickContainer.AddPipeline(tickPipeline);

        manager.SetContainer(TRADING_INIT, initContainer);
        manager.SetContainer(TRADING_TICK, tickContainer);

        // 測試執行特定事件
        manager.Execute(TRADING_INIT);
        RecordResult(Assert::AssertTrue("執行_初始化容器已執行", initContainer.IsExecuted()));
        RecordResult(Assert::AssertFalse("執行_Tick容器未執行", tickContainer.IsExecuted()));

        // 測試執行另一個事件
        manager.Execute(TRADING_TICK);
        RecordResult(Assert::AssertTrue("執行_Tick容器已執行", tickContainer.IsExecuted()));

        delete manager;
    }

    // 測試按事件重置
    void TestRestoreByEvent()
    {
        Print("=== 測試按事件重置 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("重置測試");

        // 創建容器和流水線
        TradingPipelineContainer* container = new TradingPipelineContainer("重置容器", "重置測試", "Container");
        TestManagerTradingPipeline* pipeline = new TestManagerTradingPipeline("重置流水線", "重置測試");

        container.AddPipeline(pipeline);
        manager.SetContainer(TRADING_INIT, container);

        // 執行然後重置
        manager.Execute(TRADING_INIT);
        RecordResult(Assert::AssertTrue("重置_執行後容器已執行", container.IsExecuted()));

        manager.Restore(TRADING_INIT);
        RecordResult(Assert::AssertFalse("重置_重置後容器未執行", container.IsExecuted()));

        delete manager;
    }

    // 測試清理功能
    void TestClear()
    {
        Print("=== 測試清理功能 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("清理測試");

        // 添加容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("清理容器1", "測試1", "Container");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("清理容器2", "測試2", "Container");

        manager.SetContainer(TRADING_INIT, container1);
        manager.SetContainer(TRADING_TICK, container2);

        RecordResult(Assert::AssertEquals("清理_清理前數量", 2, manager.GetContainerCount()));

        // 執行清理
        manager.Clear();

        RecordResult(Assert::AssertEquals("清理_清理後數量", 0, manager.GetContainerCount()));
        RecordResult(Assert::AssertTrue("清理_清理後為空", manager.IsEmpty()));
        RecordResult(Assert::AssertFalse("清理_清理後未執行", manager.IsExecuted()));

        delete container1;
        delete container2;
        delete manager;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("=== 測試邊界情況 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("邊界測試", "EdgeTest", false, 2);

        // 測試最大容器限制
        TradingPipelineContainer* container1 = new TradingPipelineContainer("邊界容器1", "測試1", "Container");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("邊界容器2", "測試2", "Container");
        TradingPipelineContainer* container3 = new TradingPipelineContainer("邊界容器3", "測試3", "Container");

        RecordResult(Assert::AssertTrue("邊界_添加第1個", manager.SetContainer(TRADING_INIT, container1)));
        RecordResult(Assert::AssertTrue("邊界_添加第2個", manager.SetContainer(TRADING_TICK, container2)));
        RecordResult(Assert::AssertTrue("邊界_已滿", manager.IsFull()));
        RecordResult(Assert::AssertFalse("邊界_無空位", manager.HasEmptySlot()));

        // 嘗試超過限制
        RecordResult(Assert::AssertFalse("邊界_超過限制失敗", manager.SetContainer(TRADING_DEINIT, container3)));

        // 測試禁用狀態執行
        manager.SetEnabled(false);
        manager.Execute(TRADING_INIT);
        RecordResult(Assert::AssertFalse("邊界_禁用時不執行", manager.IsExecuted()));

        delete container1;
        delete container2;
        delete container3;
        delete manager;
    }
};

//+------------------------------------------------------------------+
//| 運行 TradingPipelineContainerManager 測試的函數                 |
//+------------------------------------------------------------------+
void RunTestTradingPipelineContainerManagerFixed()
{
    Print("\n🧪 開始執行 TradingPipelineContainerManager 修正版測試...");

    TestTradingPipelineContainerManagerFixed* testCase = new TestTradingPipelineContainerManagerFixed();
    testCase.RunTests();
    delete testCase;

    Print("✅ TradingPipelineContainerManager 修正版測試執行完成\n");
}

//+------------------------------------------------------------------+
//| 運行測試（使用外部 TestRunner）                                 |
//+------------------------------------------------------------------+
void RunTestTradingPipelineContainerManagerFixed(TestRunner* runner)
{
    if(runner == NULL)
    {
        RunTestTradingPipelineContainerManagerFixed();
        return;
    }

    Print("\n🧪 執行 TradingPipelineContainerManager 修正版測試（外部 TestRunner）...");

    TestTradingPipelineContainerManagerFixed* testCase = new TestTradingPipelineContainerManagerFixed(runner);
    testCase.RunTests();
    delete testCase;

    Print("✅ TradingPipelineContainerManager 修正版測試完成\n");
}
