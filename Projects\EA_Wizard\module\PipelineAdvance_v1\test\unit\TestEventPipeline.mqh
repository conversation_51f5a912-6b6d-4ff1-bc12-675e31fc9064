//+------------------------------------------------------------------+
//|                                              TestEventPipeline.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單交易流水線                                           |
//+------------------------------------------------------------------+
class TestEventTradingPipeline : public TradingPipeline
{
private:
    string m_testMessage;

public:
    TestEventTradingPipeline(string name, string testMessage = "測試執行")
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage)
    {
    }

protected:
    virtual void Main() override
    {
        Print(StringFormat("[%s] 執行: %s", GetName(), m_testMessage));
    }
};

//+------------------------------------------------------------------+
//| EventPipeline 測試類                                            |
//+------------------------------------------------------------------+
class TestEventPipelineCase : public TestCase
{
private:
    TestRunner* m_runner;
    bool m_ownsRunner;

public:
    // 構造函數 - 可以接受外部 TestRunner 或創建內部 TestRunner
    TestEventPipelineCase(TestRunner* externalRunner = NULL) : TestCase("TestEventPipeline")
    {
        if(externalRunner != NULL)
        {
            m_runner = externalRunner;
            m_ownsRunner = false;
        }
        else
        {
            m_runner = new TestRunner();
            m_ownsRunner = true;
        }
    }

    ~TestEventPipelineCase()
    {
        if(m_ownsRunner && m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    virtual void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestEventProperty();
        TestInheritedFunctionality();
        TestPipelineManagement();
        TestExecution();
        TestEdgeCases();

        // 只有當擁有內部 TestRunner 時才顯示摘要
        if(m_ownsRunner)
        {
            m_runner.ShowSummary();
        }
    }

protected:
    // 記錄測試結果的輔助方法
    void RecordResult(TestResult* result)
    {
        m_runner.RecordResult(result);
    }

    // 測試構造函數
    void TestConstructor()
    {
        Print("=== 測試構造函數 ===");

        // 測試默認參數構造
        EventPipeline* pipeline1 = new EventPipeline("事件流水線1", TRADING_INIT);
        RecordResult(Assert::AssertNotNull("構造_默認參數", pipeline1));
        RecordResult(Assert::AssertEquals("構造_默認名稱", "事件流水線1", pipeline1.GetName()));
        RecordResult(Assert::AssertEquals("構造_默認類型", "EventPipeline", pipeline1.GetType()));
        RecordResult(Assert::AssertEquals("構造_事件類型", TRADING_INIT, pipeline1.GetEvent()));

        // 測試完整參數構造
        EventPipeline* pipeline2 = new EventPipeline(
            "事件流水線2", 
            TRADING_TICK, 
            "Tick事件處理流水線", 
            "CustomEventPipeline", 
            true, 
            100
        );
        RecordResult(Assert::AssertEquals("構造_自定義名稱", "事件流水線2", pipeline2.GetName()));
        RecordResult(Assert::AssertEquals("構造_自定義描述", "Tick事件處理流水線", pipeline2.GetDescription()));
        RecordResult(Assert::AssertEquals("構造_自定義類型", "CustomEventPipeline", pipeline2.GetType()));
        RecordResult(Assert::AssertEquals("構造_自定義事件", TRADING_TICK, pipeline2.GetEvent()));
        RecordResult(Assert::AssertEquals("構造_自定義最大流水線數", 100, pipeline2.GetMaxPipelines()));

        delete pipeline1;
        delete pipeline2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("=== 測試基本屬性 ===");

        EventPipeline* pipeline = new EventPipeline("屬性測試", TRADING_DEINIT, "清理事件流水線");

        // 測試基本屬性
        RecordResult(Assert::AssertEquals("屬性_名稱", "屬性測試", pipeline.GetName()));
        RecordResult(Assert::AssertEquals("屬性_類型", "EventPipeline", pipeline.GetType()));
        RecordResult(Assert::AssertEquals("屬性_描述", "清理事件流水線", pipeline.GetDescription()));
        RecordResult(Assert::AssertTrue("屬性_初始啟用", pipeline.IsEnabled()));
        RecordResult(Assert::AssertFalse("屬性_初始未執行", pipeline.IsExecuted()));
        RecordResult(Assert::AssertTrue("屬性_初始為空", pipeline.IsEmpty()));

        delete pipeline;
    }

    // 測試事件屬性
    void TestEventProperty()
    {
        Print("=== 測試事件屬性 ===");

        // 測試不同事件類型
        EventPipeline* initPipeline = new EventPipeline("初始化事件", TRADING_INIT);
        EventPipeline* tickPipeline = new EventPipeline("Tick事件", TRADING_TICK);
        EventPipeline* deinitPipeline = new EventPipeline("清理事件", TRADING_DEINIT);

        RecordResult(Assert::AssertEquals("事件_初始化", TRADING_INIT, initPipeline.GetEvent()));
        RecordResult(Assert::AssertEquals("事件_Tick", TRADING_TICK, tickPipeline.GetEvent()));
        RecordResult(Assert::AssertEquals("事件_清理", TRADING_DEINIT, deinitPipeline.GetEvent()));

        delete initPipeline;
        delete tickPipeline;
        delete deinitPipeline;
    }

    // 測試繼承的功能
    void TestInheritedFunctionality()
    {
        Print("=== 測試繼承的功能 ===");

        EventPipeline* pipeline = new EventPipeline("繼承測試", TRADING_INIT, "測試繼承功能");

        // 測試啟用/禁用
        pipeline.SetEnabled(false);
        RecordResult(Assert::AssertFalse("繼承_禁用", pipeline.IsEnabled()));
        pipeline.SetEnabled(true);
        RecordResult(Assert::AssertTrue("繼承_重新啟用", pipeline.IsEnabled()));

        // 測試容器功能
        RecordResult(Assert::AssertTrue("繼承_初始為空", pipeline.IsEmpty()));
        RecordResult(Assert::AssertEquals("繼承_初始流水線數", 0, pipeline.GetPipelineCount()));

        delete pipeline;
    }

    // 測試流水線管理
    void TestPipelineManagement()
    {
        Print("=== 測試流水線管理 ===");

        EventPipeline* pipeline = new EventPipeline("管理測試", TRADING_TICK, "流水線管理測試", "EventPipeline", false, 10);

        // 創建子流水線
        TestEventTradingPipeline* subPipeline1 = new TestEventTradingPipeline("子流水線1", "管理測試1");
        TestEventTradingPipeline* subPipeline2 = new TestEventTradingPipeline("子流水線2", "管理測試2");

        // 測試添加流水線
        bool added1 = pipeline.AddPipeline(subPipeline1);
        bool added2 = pipeline.AddPipeline(subPipeline2);
        RecordResult(Assert::AssertTrue("管理_添加第1個", added1));
        RecordResult(Assert::AssertTrue("管理_添加第2個", added2));
        RecordResult(Assert::AssertEquals("管理_流水線數量", 2, pipeline.GetPipelineCount()));
        RecordResult(Assert::AssertFalse("管理_非空", pipeline.IsEmpty()));

        // 測試查找流水線
        ITradingPipeline* found = pipeline.GetPipelineByName("子流水線1");
        RecordResult(Assert::AssertNotNull("管理_查找流水線", found));
        if(found != NULL)
        {
            RecordResult(Assert::AssertEquals("管理_查找名稱", "子流水線1", found.GetName()));
        }

        // 測試移除流水線
        bool removed = pipeline.RemovePipelineByName("子流水線1");
        RecordResult(Assert::AssertTrue("管理_移除成功", removed));
        RecordResult(Assert::AssertEquals("管理_移除後數量", 1, pipeline.GetPipelineCount()));

        delete pipeline;
    }

    // 測試執行
    void TestExecution()
    {
        Print("=== 測試執行 ===");

        EventPipeline* pipeline = new EventPipeline("執行測試", TRADING_INIT, "執行測試流水線");

        // 添加子流水線
        TestEventTradingPipeline* subPipeline = new TestEventTradingPipeline("執行子流水線", "執行測試");
        pipeline.AddPipeline(subPipeline);

        // 測試執行
        RecordResult(Assert::AssertFalse("執行_初始未執行", pipeline.IsExecuted()));
        pipeline.Execute();
        RecordResult(Assert::AssertTrue("執行_執行後狀態", pipeline.IsExecuted()));

        // 測試重置
        pipeline.Restore();
        RecordResult(Assert::AssertFalse("執行_重置後狀態", pipeline.IsExecuted()));

        delete pipeline;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("=== 測試邊界情況 ===");

        // 測試最大流水線限制
        EventPipeline* pipeline = new EventPipeline("邊界測試", TRADING_TICK, "邊界測試", "EventPipeline", false, 2);

        TestEventTradingPipeline* sub1 = new TestEventTradingPipeline("邊界子1", "邊界測試1");
        TestEventTradingPipeline* sub2 = new TestEventTradingPipeline("邊界子2", "邊界測試2");
        TestEventTradingPipeline* sub3 = new TestEventTradingPipeline("邊界子3", "邊界測試3");

        RecordResult(Assert::AssertTrue("邊界_添加第1個", pipeline.AddPipeline(sub1)));
        RecordResult(Assert::AssertTrue("邊界_添加第2個", pipeline.AddPipeline(sub2)));
        RecordResult(Assert::AssertTrue("邊界_已滿", pipeline.IsFull()));
        RecordResult(Assert::AssertFalse("邊界_超過限制失敗", pipeline.AddPipeline(sub3)));

        delete sub3; // 未添加的流水線需要手動刪除
        delete pipeline;

        // 測試禁用狀態執行
        EventPipeline* disabledPipeline = new EventPipeline("禁用測試", TRADING_DEINIT);
        disabledPipeline.SetEnabled(false);
        disabledPipeline.Execute();
        RecordResult(Assert::AssertFalse("邊界_禁用時不執行", disabledPipeline.IsExecuted()));

        delete disabledPipeline;
    }
};

//+------------------------------------------------------------------+
//| 運行 EventPipeline 測試的函數                                   |
//+------------------------------------------------------------------+
void RunTestEventPipeline()
{
    Print("\n🧪 開始執行 EventPipeline 單元測試...");
    
    TestEventPipelineCase* testCase = new TestEventPipelineCase();
    testCase.RunTests();
    delete testCase;
    
    Print("✅ EventPipeline 單元測試執行完成\n");
}

//+------------------------------------------------------------------+
//| 運行 EventPipeline 測試（使用外部 TestRunner）                 |
//+------------------------------------------------------------------+
void RunTestEventPipeline(TestRunner* runner)
{
    if(runner == NULL) 
    {
        RunTestEventPipeline();
        return;
    }
    
    Print("\n🧪 執行 EventPipeline 單元測試（外部 TestRunner）...");
    
    TestEventPipelineCase* testCase = new TestEventPipelineCase(runner);
    testCase.RunTests();
    delete testCase;
    
    Print("✅ EventPipeline 單元測試完成\n");
}
