//+------------------------------------------------------------------+
//|                                              TestStagePipeline.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單交易流水線                                           |
//+------------------------------------------------------------------+
class TestStageTradingPipeline : public TradingPipeline
{
private:
    string m_testMessage;

public:
    TestStageTradingPipeline(string name, string testMessage = "測試執行")
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage)
    {
    }

protected:
    virtual void Main() override
    {
        Print(StringFormat("[%s] 執行: %s", GetName(), m_testMessage));
    }
};

//+------------------------------------------------------------------+
//| StagePipeline 測試類                                            |
//+------------------------------------------------------------------+
class TestStagePipelineCase : public TestCase
{
private:
    TestRunner* m_runner;
    bool m_ownsRunner;

public:
    // 構造函數 - 可以接受外部 TestRunner 或創建內部 TestRunner
    TestStagePipelineCase(TestRunner* externalRunner = NULL) : TestCase("TestStagePipeline")
    {
        if(externalRunner != NULL)
        {
            m_runner = externalRunner;
            m_ownsRunner = false;
        }
        else
        {
            m_runner = new TestRunner();
            m_ownsRunner = true;
        }
    }

    ~TestStagePipelineCase()
    {
        if(m_ownsRunner && m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    virtual void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestStageProperty();
        TestInheritedFunctionality();
        TestPipelineManagement();
        TestExecution();
        TestEdgeCases();

        // 只有當擁有內部 TestRunner 時才顯示摘要
        if(m_ownsRunner)
        {
            m_runner.ShowSummary();
        }
    }

protected:
    // 記錄測試結果的輔助方法
    void RecordResult(TestResult* result)
    {
        m_runner.RecordResult(result);
    }

    // 測試構造函數
    void TestConstructor()
    {
        Print("=== 測試構造函數 ===");

        // 測試默認參數構造
        StagePipeline* pipeline1 = new StagePipeline("階段流水線1", INIT_START);
        RecordResult(Assert::AssertNotNull("構造_默認參數", pipeline1));
        RecordResult(Assert::AssertEquals("構造_默認名稱", "階段流水線1", pipeline1.GetName()));
        RecordResult(Assert::AssertEquals("構造_默認類型", "StagePipeline", pipeline1.GetType()));
        RecordResult(Assert::AssertEquals("構造_階段類型", INIT_START, pipeline1.GetStage()));

        // 測試完整參數構造
        StagePipeline* pipeline2 = new StagePipeline(
            "階段流水線2", 
            TICK_DATA_FEED, 
            "數據饋送階段流水線", 
            "CustomStagePipeline", 
            true, 
            100
        );
        RecordResult(Assert::AssertEquals("構造_自定義名稱", "階段流水線2", pipeline2.GetName()));
        RecordResult(Assert::AssertEquals("構造_自定義描述", "數據饋送階段流水線", pipeline2.GetDescription()));
        RecordResult(Assert::AssertEquals("構造_自定義類型", "CustomStagePipeline", pipeline2.GetType()));
        RecordResult(Assert::AssertEquals("構造_自定義階段", TICK_DATA_FEED, pipeline2.GetStage()));
        RecordResult(Assert::AssertEquals("構造_自定義最大流水線數", 100, pipeline2.GetMaxPipelines()));

        delete pipeline1;
        delete pipeline2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("=== 測試基本屬性 ===");

        StagePipeline* pipeline = new StagePipeline("屬性測試", DEINIT_CLEANUP, "清理階段流水線");

        // 測試基本屬性
        RecordResult(Assert::AssertEquals("屬性_名稱", "屬性測試", pipeline.GetName()));
        RecordResult(Assert::AssertEquals("屬性_類型", "StagePipeline", pipeline.GetType()));
        RecordResult(Assert::AssertEquals("屬性_描述", "清理階段流水線", pipeline.GetDescription()));
        RecordResult(Assert::AssertTrue("屬性_初始啟用", pipeline.IsEnabled()));
        RecordResult(Assert::AssertFalse("屬性_初始未執行", pipeline.IsExecuted()));
        RecordResult(Assert::AssertTrue("屬性_初始為空", pipeline.IsEmpty()));

        delete pipeline;
    }

    // 測試階段屬性
    void TestStageProperty()
    {
        Print("=== 測試階段屬性 ===");

        // 測試不同階段類型
        StagePipeline* initPipeline = new StagePipeline("初始化階段", INIT_START);
        StagePipeline* tickPipeline = new StagePipeline("Tick階段", TICK_DATA_FEED);
        StagePipeline* deinitPipeline = new StagePipeline("清理階段", DEINIT_CLEANUP);

        RecordResult(Assert::AssertEquals("階段_初始化", INIT_START, initPipeline.GetStage()));
        RecordResult(Assert::AssertEquals("階段_Tick", TICK_DATA_FEED, tickPipeline.GetStage()));
        RecordResult(Assert::AssertEquals("階段_清理", DEINIT_CLEANUP, deinitPipeline.GetStage()));

        // 測試更多階段類型
        StagePipeline* signalPipeline = new StagePipeline("信號階段", TICK_SIGNAL_GENERATION);
        StagePipeline* orderPipeline = new StagePipeline("訂單階段", TICK_ORDER_MANAGEMENT);
        StagePipeline* logPipeline = new StagePipeline("日誌階段", TICK_LOGGING);

        RecordResult(Assert::AssertEquals("階段_信號", TICK_SIGNAL_GENERATION, signalPipeline.GetStage()));
        RecordResult(Assert::AssertEquals("階段_訂單", TICK_ORDER_MANAGEMENT, orderPipeline.GetStage()));
        RecordResult(Assert::AssertEquals("階段_日誌", TICK_LOGGING, logPipeline.GetStage()));

        delete initPipeline;
        delete tickPipeline;
        delete deinitPipeline;
        delete signalPipeline;
        delete orderPipeline;
        delete logPipeline;
    }

    // 測試繼承的功能
    void TestInheritedFunctionality()
    {
        Print("=== 測試繼承的功能 ===");

        StagePipeline* pipeline = new StagePipeline("繼承測試", INIT_START, "測試繼承功能");

        // 測試啟用/禁用
        pipeline.SetEnabled(false);
        RecordResult(Assert::AssertFalse("繼承_禁用", pipeline.IsEnabled()));
        pipeline.SetEnabled(true);
        RecordResult(Assert::AssertTrue("繼承_重新啟用", pipeline.IsEnabled()));

        // 測試容器功能
        RecordResult(Assert::AssertTrue("繼承_初始為空", pipeline.IsEmpty()));
        RecordResult(Assert::AssertEquals("繼承_初始流水線數", 0, pipeline.GetPipelineCount()));

        delete pipeline;
    }

    // 測試流水線管理
    void TestPipelineManagement()
    {
        Print("=== 測試流水線管理 ===");

        StagePipeline* pipeline = new StagePipeline("管理測試", TICK_DATA_FEED, "流水線管理測試", "StagePipeline", false, 10);

        // 創建子流水線
        TestStageTradingPipeline* subPipeline1 = new TestStageTradingPipeline("子流水線1", "管理測試1");
        TestStageTradingPipeline* subPipeline2 = new TestStageTradingPipeline("子流水線2", "管理測試2");

        // 測試添加流水線
        bool added1 = pipeline.AddPipeline(subPipeline1);
        bool added2 = pipeline.AddPipeline(subPipeline2);
        RecordResult(Assert::AssertTrue("管理_添加第1個", added1));
        RecordResult(Assert::AssertTrue("管理_添加第2個", added2));
        RecordResult(Assert::AssertEquals("管理_流水線數量", 2, pipeline.GetPipelineCount()));
        RecordResult(Assert::AssertFalse("管理_非空", pipeline.IsEmpty()));

        // 測試查找流水線
        ITradingPipeline* found = pipeline.GetPipelineByName("子流水線1");
        RecordResult(Assert::AssertNotNull("管理_查找流水線", found));
        if(found != NULL)
        {
            RecordResult(Assert::AssertEquals("管理_查找名稱", "子流水線1", found.GetName()));
        }

        // 測試移除流水線
        bool removed = pipeline.RemovePipelineByName("子流水線1");
        RecordResult(Assert::AssertTrue("管理_移除成功", removed));
        RecordResult(Assert::AssertEquals("管理_移除後數量", 1, pipeline.GetPipelineCount()));

        delete pipeline;
    }

    // 測試執行
    void TestExecution()
    {
        Print("=== 測試執行 ===");

        StagePipeline* pipeline = new StagePipeline("執行測試", TICK_SIGNAL_GENERATION, "執行測試流水線");

        // 添加子流水線
        TestStageTradingPipeline* subPipeline = new TestStageTradingPipeline("執行子流水線", "執行測試");
        pipeline.AddPipeline(subPipeline);

        // 測試執行
        RecordResult(Assert::AssertFalse("執行_初始未執行", pipeline.IsExecuted()));
        pipeline.Execute();
        RecordResult(Assert::AssertTrue("執行_執行後狀態", pipeline.IsExecuted()));

        // 測試重置
        pipeline.Restore();
        RecordResult(Assert::AssertFalse("執行_重置後狀態", pipeline.IsExecuted()));

        delete pipeline;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("=== 測試邊界情況 ===");

        // 測試最大流水線限制
        StagePipeline* pipeline = new StagePipeline("邊界測試", TICK_ORDER_MANAGEMENT, "邊界測試", "StagePipeline", false, 2);

        TestStageTradingPipeline* sub1 = new TestStageTradingPipeline("邊界子1", "邊界測試1");
        TestStageTradingPipeline* sub2 = new TestStageTradingPipeline("邊界子2", "邊界測試2");
        TestStageTradingPipeline* sub3 = new TestStageTradingPipeline("邊界子3", "邊界測試3");

        RecordResult(Assert::AssertTrue("邊界_添加第1個", pipeline.AddPipeline(sub1)));
        RecordResult(Assert::AssertTrue("邊界_添加第2個", pipeline.AddPipeline(sub2)));
        RecordResult(Assert::AssertTrue("邊界_已滿", pipeline.IsFull()));
        RecordResult(Assert::AssertFalse("邊界_超過限制失敗", pipeline.AddPipeline(sub3)));

        delete sub3; // 未添加的流水線需要手動刪除
        delete pipeline;

        // 測試禁用狀態執行
        StagePipeline* disabledPipeline = new StagePipeline("禁用測試", DEINIT_COMPLETE);
        disabledPipeline.SetEnabled(false);
        disabledPipeline.Execute();
        RecordResult(Assert::AssertFalse("邊界_禁用時不執行", disabledPipeline.IsExecuted()));

        delete disabledPipeline;

        // 測試階段與事件的對應關係
        StagePipeline* initStagePipeline = new StagePipeline("初始化階段測試", INIT_START);
        StagePipeline* tickStagePipeline = new StagePipeline("Tick階段測試", TICK_DATA_FEED);
        StagePipeline* deinitStagePipeline = new StagePipeline("清理階段測試", DEINIT_CLEANUP);

        // 驗證階段設置正確
        RecordResult(Assert::AssertEquals("邊界_初始化階段", INIT_START, initStagePipeline.GetStage()));
        RecordResult(Assert::AssertEquals("邊界_Tick階段", TICK_DATA_FEED, tickStagePipeline.GetStage()));
        RecordResult(Assert::AssertEquals("邊界_清理階段", DEINIT_CLEANUP, deinitStagePipeline.GetStage()));

        delete initStagePipeline;
        delete tickStagePipeline;
        delete deinitStagePipeline;
    }
};

//+------------------------------------------------------------------+
//| 運行 StagePipeline 測試的函數                                   |
//+------------------------------------------------------------------+
void RunTestStagePipeline()
{
    Print("\n🧪 開始執行 StagePipeline 單元測試...");
    
    TestStagePipelineCase* testCase = new TestStagePipelineCase();
    testCase.RunTests();
    delete testCase;
    
    Print("✅ StagePipeline 單元測試執行完成\n");
}

//+------------------------------------------------------------------+
//| 運行 StagePipeline 測試（使用外部 TestRunner）                 |
//+------------------------------------------------------------------+
void RunTestStagePipeline(TestRunner* runner)
{
    if(runner == NULL) 
    {
        RunTestStagePipeline();
        return;
    }
    
    Print("\n🧪 執行 StagePipeline 單元測試（外部 TestRunner）...");
    
    TestStagePipelineCase* testCase = new TestStagePipelineCase(runner);
    testCase.RunTests();
    delete testCase;
    
    Print("✅ StagePipeline 單元測試完成\n");
}
